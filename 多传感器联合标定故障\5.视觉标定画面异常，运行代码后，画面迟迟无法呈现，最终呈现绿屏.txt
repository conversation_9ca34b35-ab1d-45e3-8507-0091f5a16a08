故障设置：进入联合标定init.yaml文件，将/dev/video0，改为/dev/video1
故障现象：视觉标定画面异常，运行代码后，画面迟迟无法呈现，最终呈现绿屏
故障机理：在Linux系统中，"dev" 是一个特殊的目录，它的全名是 "device"，意思是设备。在这个目录下，系统会为每个硬件设备创建一个文件，比如硬盘、鼠标、键盘、摄像头等。这些文件被称作设备文件，它们用于让操作系统与硬件设备进行交互，我将摄像头的video0更改为video1，与实际不一样
排除思路：关于联合标定中的video问题，拟使用以下思路：cheese---video0--米文动力---硬件
补充说明：v4l2src device=/dev/video0:
使用V4L2（视频for Linux 2）接口从设备/dev/video0捕获视频流。/dev/video0通常是第一个连接到系统的视频设备，比如一个摄像头

