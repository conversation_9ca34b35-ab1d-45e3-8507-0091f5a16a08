故障设置：将param_settings.py文件中第19，20行改为main_w=main_end_h+main_middle_w
main_h=main_end_w+main_middle_h
故障现象：报错IndexError: list index out of range
故障机理：环视param_settings.py文件中书写了关于环视的参数以及计算公式，其中main_w为总体标定区域宽度，应该为上下标定区域宽度加上2个左右标定区域宽度，高度同理，如果我将公式改变，将导致程序计算异常，最终无法呈现结果
排除思路：按照正常步骤进行环视标定，报错索引错误（indexerror），百分之90都是param_settings.py文件出现了异常，并且大部分可能是公式出现了问题
补充说明：根据描述，应该为main_w=2main_end_w +main_middle_w，但是实际上却是main_w=2main_end_h+main_middle_w，由于代码是开源代码，经过了魔改，与手册描述的不一致，猜测是为了实现某种功能做了妥协，修改了代码，但是由于数值不会相差太大，所以不做讨论，只需要不要误报就行

