故障设置：进入环视__init__.py文件，将第3行from .capture_thread import CaptureThread中的CaptureThread更改为其他
故障现象：报错ImportError: cannot import name 'CaptureThrea' from 'surround_view.capture_thread
故障机理：由于在此__init__.py文件中，又细化规定了从包中导入特定的类和方法，如果我将__init__.py文件中的内容更改，将导致程序报错
排除思路：此故障指向性强，无法从模块 surround_view.capture_thread 中导入名为 CaptureThrea 的对象，这里值得注意的是：报错所抛出的导入错误的名称以及内容都是正确的，可以借此直接与文件内容进行比对，以此达到快速排故的目的
补充说明：__init__.py 文件用于标识包、初始化包和控制包的导入行为，在Python 2.x中是必需的，Python 3.x中是可选但常用，可以为空，包含初始化代码或定义包的公开接口，说白了，有这个文件那么就是一个包，如果没有只能说明是一个文件，同时这个init.py文件还可以提前控制包的导入

