故障设置：进入orin环视surround_view1.1--images目录中，将car.png图片删除或者更改名称
故障现象：报错环视param_settings.py文件中car_image = cv2.resize(car_image, (xr - xl, yb - yt)) 指定行CV2错误
故障机理：由于环视，是通过截取对应的摄像头的特定区域进行拼接，来显示画面，而中心则需要一张车辆的图片来填补空白，如果删除这张图片，任何一步都将无法完成
排除思路：这张图片的故障指向性非常强，只需要先检查位于images文件中的car.png，其次检查param_settings.py文件
补充说明：之所以会报错car_image = cv2.resize(car_image, (xr - xl, yb - yt)) ，是因为这一行是在处理程序读取到的照片进行处理，出宫删除或者更改名字，那么程序将无法进行处理改图像，就会报错

