故障设置：进入surround_view目录下，将以.so结尾的文件更改，以鱼眼相机为例
故障现象：报错from .fisheye_camera import FisheyeCameraModel
ModuleNotFoundError: No module named 'surround_view.fisheye_camera'
故障机理：这个改变的就是位于surround_view目录下的.so文件，这些文件首先被__init__.py文件所管辖标识，如果文件错误，将导致无法导入对应的模块，进而报错
排除思路：这个报错信息表明，在尝试导入模块 fisheye_camera 时，Python 没有找到该模块。具体错误消息为：
错误类型：ModuleNotFoundError
具体问题：没有名为 surround_view.fisheye_camera 的模块。这里直接指向了目录下的,so结尾的文件，并不是导入错误，而是直接没有发现错误
补充说明：ModuleNotFoundError 是 ImportError 的具体子类，专门用来处理模块或包未找到的情况。
ImportError 是更广义的导入失败异常，包括模块未找到以及其他导入错误

