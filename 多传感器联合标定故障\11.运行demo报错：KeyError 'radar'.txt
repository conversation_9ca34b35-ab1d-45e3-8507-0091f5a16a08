故障设置：在联合标定主目录下，将config.ini文件更名或者删除
故障现象：报错KeyError: 'radar'
故障机理：config.ini是联合标定中的设置文件，采用ini格式书写，其中毫米波lidar以及摄像头的参数设置就写在之中，如果更改，程序无法读取到其中的数值，就会报错
排除思路：此故障由于具有一定的迷惑性，但是需要理解其具体原理，首先，demo会率先读取radar，其次是lidar与camera，如果config被更改了，程序会首先抛出第一个读取不到的radar，并且，进入config文件，所有的字体颜色会消失
补充说明：在Python中，.ini 文件是一种用来存储配置信息的文本文件格式。它通常由多个键值对和节（section）组成，用来配置应用程序的一些参数。虽然Python标准库没有专门的模块来处理.ini文件，但可以使用标准库中的 configparser 模块来读写 .ini 文件。

