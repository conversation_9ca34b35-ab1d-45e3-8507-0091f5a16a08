故障设置：进入联合标定config.ini文件中，将lidar名称'LS32'更改为'LS23'
故障现象：lidar=LsLidarRead()
NameError: name 'LsLidarRead' is not defined
故障机理：还是关于config中的内容故障，如果更改了lidar的节(解释见补充说明)，更改后，将导致demo.py无法读取到config文件中的lidar
排除思路：程序会率先抛出所在多少行对其进行的引用，由于上文，读取config后，程序并没有读取到LS32的节，导致程序下文无法开启接收lidar数据，需要根据代码的逻辑，不过，为了提升速度，config等一系列基础的文件，采取直接开始就检查的方法
补充说明：节（section）：节是配置文件中的一个分组，用于将相关联的配置信息组织在一起。每个节都有一个名称，通常用方括号 [] 括起来。例如：[camera],[radar],[lidar]
选项（option）：选项是节中的一个具体配置项，用于存储具体的配置数值或字符串。每个选项都有一个名称和一个对应的值，通常采用 key = value 的形式。   

