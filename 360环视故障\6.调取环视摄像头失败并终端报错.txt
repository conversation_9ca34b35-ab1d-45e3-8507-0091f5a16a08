故障设置：进入环视images文件夹中，将car.png或者另外几张图片更改为以jpg结尾的格式
故障现象：报错cv2.error: 0pencV(4.2.0)../modules/imgproc/src/imgwarp.cpp:668: error:(-215:ASsertion failed)!ssize.empty()in function'remapBilinear
故障机理：由于orin与联合标定各自的特性，选择了不同类型的图片格式，如果更改，将导致系统无法识别
排除思路：由于报错晦涩难懂，指向性并不明确，需要理解jpg与png图片格式的特点，在报错检查时，需要快速反应过来
补充说明：PNG是一种使用无损压缩，支持透明度和高颜色深度的图像格式，适用于需要高质量和透明度的图像，如图标和标志；而JPEG是一种使用有损压缩的图像格式，不支持透明度，适用于复杂的照片和色彩丰富的图像，文件大小更小。

