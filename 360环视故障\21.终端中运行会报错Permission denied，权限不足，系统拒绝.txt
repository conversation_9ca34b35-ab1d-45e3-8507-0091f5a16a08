故障设置：在环视surround_view1.1目录下运行以下指令sudo chmod 000 run_get_weight_matrices.py
故障现象：终端中运行会报错Permission denied，权限不足，系统拒绝
故障机理：这个代码是linux中的权限指令，也就是chmod命令，可以给任何文件赋予权限，如果失去了权限，将无法运行和执行，甚至连查看都无法查看
排除思路：在Linux中可以通过ls -l命令查看文件的权限，文件有可读（r）、可写（w）、可执行（x）权限，如果被设置为000，将会是-----任何权限都没有，恢复也只需要将chmod 000 给为777
补充说明：chmod 命令可以通过两种方式来修改文件权限：符号模式和数字模式。它们都能达到相同的目的，但语法和使用方式有所不同，至于用符号模式见批注，需要注意的是，在Linux可视化交互图像界面，会直接在文件上显示锁扣象征权限关闭的符号

