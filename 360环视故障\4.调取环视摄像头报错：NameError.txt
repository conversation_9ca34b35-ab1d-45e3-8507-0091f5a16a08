故障设置：环视param_settings.py文件，第48-51行，将yt yt xl xl进行互换为xt xt yl yl 
故障现象：报错"front":(total w,xt),NameError: name 'xt'is not defined
故障机理：由于在param_settings.py文件中，有许多公式在进行环视各个区域的计算，其中project_shapes就是计算每个摄像头投影的区域大小，如果将其更改，在运行获取投影变换矩阵时，程序将会报错
排除思路：由于此故障设置出的参繁多，难以完全记住，遂映入上下文对比法，如故障报错所述，xt没有被定义，但是在上文会有定义这一类变量的代码，可据此回到上文，，寻找，由于报错的特殊性，所以此类故障可以利用上下文互相印证
补充说明：此故障点的参数变量，不仅仅在此处出现，还在下文最后以后对画面调整中，再次出现，所以提高了了上下文互相印证的可能性

