故障设置：进入联合标定文件目录下，使用chmod命令将.so文件权限设置为000
故障现象：运行demo文件报错ImportError: /home/<USER>/w2c_for_miivi/w2c.cpython-36m-aarch64-linux-gnu.so: cannot open shared object file: Permission denied
故障机理：这些共享库文件需要被导入，但是导入其实就是读取，如果将读取权限进行更改，那么就会导致程序导入失败，也就是读取失败
排除思路：报错之中会提到权限错误，需要注意在排故的时候需要严格执行步骤，不能忽略细节，这时候就需要使用ls -l命令，这个命令是在linux中查看文件权限的命令，使用之后，在每个文件对应的前方都会有rxw的组合，如果全部被取消了将会有----------来显示
补充说明：需要注意的是，chmod命令并不陌生，通常会有符号来在图标的表面来显示，但是对于共享库文件，并不显示，仍然会报错，需要十分注意，它的报错和上面的共享库文件和python包的权限错误存在相似性，需要注意！！！！

